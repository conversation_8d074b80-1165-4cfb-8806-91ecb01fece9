//+------------------------------------------------------------------+
//|                                            RiskController.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef RISK_CONTROLLER_MQH
#define RISK_CONTROLLER_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountMonitor.mqh"

//+------------------------------------------------------------------+
//| RiskController Class                                             |
//| Combines risk checking and trading control responsibilities     |
//| Responsibilities: Risk Assessment + Trading Control             |
//+------------------------------------------------------------------+
class RiskController : public BaseComponent
{
private:
    AccountProtectionConfig* m_config;              // Configuration component
    AccountMonitor*         m_monitor;              // Monitor component
    
    // Trading control state
    bool                    m_tradingHalted;        // Trading halt flag
    bool                    m_emergencyStop;        // Emergency stop flag
    string                  m_haltReason;           // Reason for trading halt
    datetime                m_haltTime;             // Time when trading was halted
    
    // Control flags
    bool                    m_allowNewOrders;       // Allow new orders flag
    bool                    m_allowOrderModification; // Allow order modification flag
    bool                    m_allowOrderClosure;    // Allow order closure flag
    
public:
    //--- Constructor and Destructor
                            RiskController(AccountProtectionConfig* config, AccountMonitor* monitor);
    virtual                ~RiskController();
    
    //--- Risk check methods
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();
    bool                    CheckSpreadLimits(string symbol = "");
    bool                    CheckAllRisks(double lotSize = 0.0, string symbol = "");
    
    //--- Trading control methods
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0, string symbol = "");
    bool                    IsOrderModificationAllowed();
    bool                    IsOrderClosureAllowed();
    
    //--- Trading halt methods
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");
    void                    ClearEmergencyStop();
    
    //--- Information methods
    bool                    IsTradingHalted() const { return m_tradingHalted; }
    bool                    IsEmergencyStop() const { return m_emergencyStop; }
    string                  GetHaltReason() const { return m_haltReason; }
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetControlSummary() const;
    
private:
    //--- Internal data access methods
    double                  GetAccountBalance() const { return AccountBalance(); }
    double                  GetAccountEquity() const { return AccountEquity(); }
    int                     GetOpenOrdersCount() const { return OrdersTotal(); }
    double                  GetTotalLotSize() const;
    double                  GetCurrentSpread(string symbol = "") const;
    
    //--- Internal control methods
    bool                    CheckBasicTradingConditions();
    void                    UpdateControlFlags();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
RiskController::RiskController(AccountProtectionConfig* config, AccountMonitor* monitor) 
    : BaseComponent("RiskController")
{
    m_config = config;
    m_monitor = monitor;
    
    m_tradingHalted = false;
    m_emergencyStop = false;
    m_haltReason = "";
    m_haltTime = 0;
    
    m_allowNewOrders = true;
    m_allowOrderModification = true;
    m_allowOrderClosure = true;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
RiskController::~RiskController()
{
    // Components are managed externally
}

//+------------------------------------------------------------------+
//| Initialize risk controller                                       |
//+------------------------------------------------------------------+
bool RiskController::OnInitialize()
{
    if (m_config == NULL)
    {
        SetError(4001, "Configuration component not set");
        return false;
    }
    
    if (m_monitor == NULL)
    {
        SetError(4002, "Monitor component not set");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate risk controller                                         |
//+------------------------------------------------------------------+
bool RiskController::OnValidate()
{
    if (m_config == NULL || m_monitor == NULL)
    {
        SetError(4003, "Component validation failed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update risk controller                                           |
//+------------------------------------------------------------------+
bool RiskController::OnUpdate()
{
    UpdateControlFlags();
    
    // Auto-halt trading if emergency status
    if (m_monitor != NULL && m_monitor.GetCurrentStatus() == STATUS_EMERGENCY && !m_emergencyStop)
    {
        EmergencyStop("Emergency status detected");
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check account limits                                             |
//+------------------------------------------------------------------+
bool RiskController::CheckAccountLimits()
{
    if (m_config == NULL || m_monitor == NULL) return false;
    
    double currentLossPercent = m_monitor.GetCurrentLossPercent();
    double maxLossPercent = m_config.GetMaxLossPercent();
    
    if (currentLossPercent >= maxLossPercent)
    {
        EmergencyStop("Maximum loss percentage exceeded");
        return false;
    }
    
    double currentDrawdownPercent = m_monitor.GetCurrentDrawdownPercent();
    double maxDrawdownPercent = m_config.GetMaxDrawdownPercent();
    
    if (currentDrawdownPercent >= maxDrawdownPercent)
    {
        EmergencyStop("Maximum drawdown percentage exceeded");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check position limits                                            |
//+------------------------------------------------------------------+
bool RiskController::CheckPositionLimits(double lotSize = 0.0)
{
    if (m_config == NULL) return false;
    
    if (GetOpenOrdersCount() >= m_config.GetMaxOpenOrders())
        return false;
    
    if (lotSize > 0.0 && lotSize > m_config.GetMaxLotSize())
        return false;
    
    if (GetTotalLotSize() + lotSize > m_config.GetMaxTotalLotSize())
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool RiskController::CheckDailyLimits()
{
    if (m_config == NULL || m_monitor == NULL) return false;
    
    double maxDailyLoss = m_config.GetMaxDailyLoss();
    if (maxDailyLoss <= 0.0) return true;
    
    double currentDailyLoss = m_monitor.GetDailyLoss();
    return (currentDailyLoss < maxDailyLoss);
}

//+------------------------------------------------------------------+
//| Check spread limits                                              |
//+------------------------------------------------------------------+
bool RiskController::CheckSpreadLimits(string symbol = "")
{
    if (m_config == NULL) return false;
    
    double currentSpread = GetCurrentSpread(symbol);
    double maxSpread = m_config.GetMaxSpread();
    
    return (currentSpread <= maxSpread);
}

//+------------------------------------------------------------------+
//| Check all risks                                                  |
//+------------------------------------------------------------------+
bool RiskController::CheckAllRisks(double lotSize = 0.0, string symbol = "")
{
    return CheckAccountLimits() && 
           CheckPositionLimits(lotSize) && 
           CheckDailyLimits() && 
           CheckSpreadLimits(symbol);
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool RiskController::IsTradingAllowed()
{
    if (m_tradingHalted || m_emergencyStop) return false;
    
    return CheckBasicTradingConditions() && CheckAllRisks();
}

//+------------------------------------------------------------------+
//| Check if new order is allowed                                    |
//+------------------------------------------------------------------+
bool RiskController::IsNewOrderAllowed(double lotSize = 0.0, string symbol = "")
{
    if (!m_allowNewOrders || !IsTradingAllowed()) return false;
    
    return CheckPositionLimits(lotSize) && CheckSpreadLimits(symbol);
}

//+------------------------------------------------------------------+
//| Check if order modification is allowed                           |
//+------------------------------------------------------------------+
bool RiskController::IsOrderModificationAllowed()
{
    if (!m_allowOrderModification || m_emergencyStop) return false;
    
    return CheckBasicTradingConditions();
}

//+------------------------------------------------------------------+
//| Check if order closure is allowed                                |
//+------------------------------------------------------------------+
bool RiskController::IsOrderClosureAllowed()
{
    return m_allowOrderClosure && CheckBasicTradingConditions();
}

//+------------------------------------------------------------------+
//| Halt trading                                                     |
//+------------------------------------------------------------------+
void RiskController::HaltTrading(string reason = "")
{
    if (!m_tradingHalted)
    {
        m_tradingHalted = true;
        m_haltReason = (reason != "") ? reason : "Trading halted";
        m_haltTime = TimeCurrent();
        
        Print("TRADING HALTED: ", m_haltReason);
    }
}

//+------------------------------------------------------------------+
//| Resume trading                                                   |
//+------------------------------------------------------------------+
void RiskController::ResumeTrading()
{
    if (m_tradingHalted && !m_emergencyStop)
    {
        m_tradingHalted = false;
        m_haltReason = "";
        m_haltTime = 0;
        
        Print("TRADING RESUMED");
    }
}

//+------------------------------------------------------------------+
//| Emergency stop                                                   |
//+------------------------------------------------------------------+
void RiskController::EmergencyStop(string reason = "")
{
    m_emergencyStop = true;
    m_tradingHalted = true;
    m_haltReason = "EMERGENCY: " + ((reason != "") ? reason : "Emergency stop activated");
    m_haltTime = TimeCurrent();
    
    m_allowNewOrders = false;
    m_allowOrderModification = false;
    
    Print("EMERGENCY STOP ACTIVATED: ", m_haltReason);
}

//+------------------------------------------------------------------+
//| Clear emergency stop                                             |
//+------------------------------------------------------------------+
void RiskController::ClearEmergencyStop()
{
    if (m_emergencyStop)
    {
        m_emergencyStop = false;
        m_tradingHalted = false;
        m_haltReason = "";
        m_haltTime = 0;
        
        m_allowNewOrders = true;
        m_allowOrderModification = true;
        m_allowOrderClosure = true;
        
        Print("EMERGENCY STOP CLEARED");
    }
}

//+------------------------------------------------------------------+
//| Get control summary                                              |
//+------------------------------------------------------------------+
string RiskController::GetControlSummary() const
{
    string summary = "Risk & Trading Control:\n";
    summary += "  Trading: " + (m_tradingHalted ? "HALTED" : "ALLOWED") + "\n";
    summary += "  Emergency: " + (m_emergencyStop ? "ACTIVE" : "INACTIVE") + "\n";
    summary += "  New Orders: " + (m_allowNewOrders ? "ALLOWED" : "BLOCKED") + "\n";
    
    if (m_haltReason != "")
        summary += "  Reason: " + m_haltReason;
    
    return summary;
}

//+------------------------------------------------------------------+
//| Get total lot size                                               |
//+------------------------------------------------------------------+
double RiskController::GetTotalLotSize() const
{
    double totalLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            totalLots += OrderLots();
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| Get current spread                                               |
//+------------------------------------------------------------------+
double RiskController::GetCurrentSpread(string symbol = "") const
{
    if (symbol == "") symbol = Symbol();
    return MarketInfo(symbol, MODE_SPREAD);
}

//+------------------------------------------------------------------+
//| Check basic trading conditions                                   |
//+------------------------------------------------------------------+
bool RiskController::CheckBasicTradingConditions()
{
    return IsTradeAllowed() && (MarketInfo(Symbol(), MODE_TRADEALLOWED) > 0);
}

//+------------------------------------------------------------------+
//| Update control flags                                             |
//+------------------------------------------------------------------+
void RiskController::UpdateControlFlags()
{
    if (m_emergencyStop) return;
    
    if (m_monitor != NULL)
    {
        ENUM_PROTECTION_STATUS status = m_monitor.GetCurrentStatus();
        
        switch(status)
        {
            case STATUS_EMERGENCY:
                m_allowNewOrders = false;
                m_allowOrderModification = false;
                break;
            case STATUS_CRITICAL:
                m_allowNewOrders = false;
                m_allowOrderModification = true;
                break;
            default:
                m_allowNewOrders = true;
                m_allowOrderModification = true;
                break;
        }
    }
}

#endif // RISK_CONTROLLER_MQH
