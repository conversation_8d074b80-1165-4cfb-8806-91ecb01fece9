//+------------------------------------------------------------------+
//|                                          AccountProtection.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_MQH
#define ACCOUNT_PROTECTION_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "AccountProtectionValidator.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountMonitor.mqh"
#include "RiskController.mqh"

//+------------------------------------------------------------------+
//| AccountProtection Class (Simplified Version)                    |
//| Refactored to follow SRP with reduced complexity                 |
//| Main responsibility: Coordinate 3 core components               |
//+------------------------------------------------------------------+
class AccountProtection : public BaseComponent
{
private:
    // Core components (simplified architecture)
    AccountProtectionConfig*    m_config;           // Configuration management
    AccountMonitor*            m_monitor;           // Monitoring + Status management
    RiskController*            m_riskController;    // Risk checking + Trading control

    // Validator component (existing)
    AccountProtectionValidator m_validator;         // Parameter validator

    // Component management
    bool                       m_componentsOwned;   // Whether we own the components

public:
    //--- Constructor and Destructor
                            AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE,
                                            bool strictMode = true,
                                            string symbol = "");
    virtual                ~AccountProtection();

    //--- Component access methods (for advanced usage)
    AccountProtectionConfig*    GetConfig() const { return m_config; }
    AccountMonitor*            GetMonitor() const { return m_monitor; }
    RiskController*            GetRiskController() const { return m_riskController; }

    //--- Configuration methods (delegated to config component)
    void                    SetProtectionLevel(ENUM_PROTECTION_LEVEL level);
    void                    SetMaxLossPercent(double percent);
    void                    SetMaxDailyLoss(double amount);
    void                    SetMaxDrawdownPercent(double percent);
    void                    SetMaxOpenOrders(int count);
    void                    SetMaxLotSize(double lotSize);
    void                    SetMaxTotalLotSize(double totalLotSize);
    void                    SetMaxSpread(double spread);

    //--- Information methods (delegated to appropriate components)
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const;
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const;
    double                  GetMaxLossPercent() const;
    double                  GetCurrentLossPercent() const;
    double                  GetCurrentDrawdownPercent() const;
    bool                    IsTradingHalted() const;

    //--- Protection check methods (delegated to risk controller)
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0, string symbol = "");
    bool                    IsSpreadAcceptable(string symbol = "");
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();

    //--- Status update methods (delegated to monitor and risk controller)
    void                    UpdateStatus();
    void                    ResetDailyCounters();
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");

    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;

    //--- Utility methods (internal data access + delegation)
    double                  GetAccountBalance() const { return AccountBalance(); }
    double                  GetAccountEquity() const { return AccountEquity(); }
    double                  GetAccountProfit() const { return AccountProfit(); }
    int                     GetOpenOrdersCount() const { return OrdersTotal(); }
    double                  GetTotalLotSize() const;
    double                  GetCurrentSpread(string symbol = "") const;
    bool                    IsNewTradingDay() const;
    string                  GetStatusDescription() const;

    //--- Summary methods
    string                  GetProtectionSummary() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountProtection::AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE,
                                   bool strictMode = true,
                                   string symbol = "") : BaseComponent("AccountProtection")
{
    // Initialize validator component
    m_validator.SetStrictMode(strictMode);

    // Create simplified component architecture
    m_config = new AccountProtectionConfig(level);
    m_monitor = new AccountMonitor();
    m_riskController = new RiskController(m_config, m_monitor);

    m_componentsOwned = true;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtection::~AccountProtection()
{
    // Cleanup components if we own them
    if (m_componentsOwned)
    {
        if (m_riskController != NULL) delete m_riskController;
        if (m_monitor != NULL) delete m_monitor;
        if (m_config != NULL) delete m_config;
    }
}

//+------------------------------------------------------------------+
//| Initialize account protection                                    |
//+------------------------------------------------------------------+
bool AccountProtection::OnInitialize()
{
    // Initialize components in dependency order
    if (!m_config.Initialize())
    {
        SetError(901, "Failed to initialize configuration: " + m_config.GetLastErrorMessage());
        return false;
    }

    if (!m_monitor.Initialize())
    {
        SetError(902, "Failed to initialize monitor: " + m_monitor.GetLastErrorMessage());
        return false;
    }

    if (!m_riskController.Initialize())
    {
        SetError(903, "Failed to initialize risk controller: " + m_riskController.GetLastErrorMessage());
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool AccountProtection::OnValidate()
{
    // Validate all components
    if (!m_config.Validate())
    {
        SetError(904, "Configuration validation failed");
        return false;
    }

    if (!m_monitor.Validate())
    {
        SetError(905, "Monitor validation failed");
        return false;
    }

    if (!m_riskController.Validate())
    {
        SetError(906, "Risk controller validation failed");
        return false;
    }

    // Validate using the existing validator
    bool validationResult = m_validator.ValidateAllProtectionParameters(
        m_config.GetMaxLossPercent(),
        m_config.GetMaxDrawdownPercent(),
        m_config.GetMaxOpenOrders(),
        m_config.GetMaxLotSize(),
        m_config.GetMaxTotalLotSize(),
        m_config.GetMaxSpread(),
        (int)m_config.GetProtectionLevel()
    );

    if (!validationResult)
    {
        SetError(907, "Parameter validation failed");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
bool AccountProtection::OnUpdate()
{
    // Update all components
    if (!m_monitor.Update())
    {
        SetError(908, "Monitor update failed");
        return false;
    }

    // Update status based on current metrics
    m_monitor.UpdateStatus(m_config.GetMaxLossPercent(), m_config.GetMaxDrawdownPercent());

    if (!m_riskController.Update())
    {
        SetError(909, "Risk controller update failed");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Configuration methods (delegated to config component)           |
//+------------------------------------------------------------------+
void AccountProtection::SetProtectionLevel(ENUM_PROTECTION_LEVEL level)
{
    if (m_config != NULL)
        m_config.SetProtectionLevel(level);
}

void AccountProtection::SetMaxLossPercent(double percent)
{
    if (m_config != NULL)
        m_config.SetMaxLossPercent(percent);
}

void AccountProtection::SetMaxDailyLoss(double amount)
{
    if (m_config != NULL)
        m_config.SetMaxDailyLoss(amount);
}

void AccountProtection::SetMaxDrawdownPercent(double percent)
{
    if (m_config != NULL)
        m_config.SetMaxDrawdownPercent(percent);
}

void AccountProtection::SetMaxOpenOrders(int count)
{
    if (m_config != NULL)
        m_config.SetMaxOpenOrders(count);
}

void AccountProtection::SetMaxLotSize(double lotSize)
{
    if (m_config != NULL)
        m_config.SetMaxLotSize(lotSize);
}

void AccountProtection::SetMaxTotalLotSize(double totalLotSize)
{
    if (m_config != NULL)
        m_config.SetMaxTotalLotSize(totalLotSize);
}

void AccountProtection::SetMaxSpread(double spread)
{
    if (m_config != NULL)
        m_config.SetMaxSpread(spread);
}

//+------------------------------------------------------------------+
//| Information methods (delegated to appropriate components)        |
//+------------------------------------------------------------------+
ENUM_PROTECTION_LEVEL AccountProtection::GetProtectionLevel() const
{
    return (m_config != NULL) ? m_config.GetProtectionLevel() : PROTECTION_MODERATE;
}

ENUM_PROTECTION_STATUS AccountProtection::GetCurrentStatus() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentStatus() : STATUS_NORMAL;
}

double AccountProtection::GetMaxLossPercent() const
{
    return (m_config != NULL) ? m_config.GetMaxLossPercent() : 20.0;
}

double AccountProtection::GetCurrentLossPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentLossPercent() : 0.0;
}

double AccountProtection::GetCurrentDrawdownPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentDrawdownPercent() : 0.0;
}

bool AccountProtection::IsTradingHalted() const
{
    return (m_riskController != NULL) ? m_riskController.IsTradingHalted() : false;
}

//+------------------------------------------------------------------+
//| Protection check methods (delegated to risk controller)         |
//+------------------------------------------------------------------+
bool AccountProtection::IsTradingAllowed()
{
    return (m_riskController != NULL) ? m_riskController.IsTradingAllowed() : false;
}

bool AccountProtection::IsNewOrderAllowed(double lotSize = 0.0, string symbol = "")
{
    return (m_riskController != NULL) ? m_riskController.IsNewOrderAllowed(lotSize, symbol) : false;
}

bool AccountProtection::IsSpreadAcceptable(string symbol = "")
{
    return (m_riskController != NULL) ? m_riskController.CheckSpreadLimits(symbol) : false;
}

bool AccountProtection::CheckAccountLimits()
{
    return (m_riskController != NULL) ? m_riskController.CheckAccountLimits() : false;
}

bool AccountProtection::CheckPositionLimits(double lotSize = 0.0)
{
    return (m_riskController != NULL) ? m_riskController.CheckPositionLimits(lotSize) : false;
}

bool AccountProtection::CheckDailyLimits()
{
    return (m_riskController != NULL) ? m_riskController.CheckDailyLimits() : false;
}

//+------------------------------------------------------------------+
//| Status update methods (delegated to components)                 |
//+------------------------------------------------------------------+
void AccountProtection::UpdateStatus()
{
    if (m_monitor != NULL && m_config != NULL)
        m_monitor.UpdateStatus(m_config.GetMaxLossPercent(), m_config.GetMaxDrawdownPercent());
}

void AccountProtection::ResetDailyCounters()
{
    if (m_monitor != NULL)
        m_monitor.ResetDailyCounters();
}

void AccountProtection::HaltTrading(string reason = "")
{
    if (m_riskController != NULL)
        m_riskController.HaltTrading(reason);
}

void AccountProtection::ResumeTrading()
{
    if (m_riskController != NULL)
        m_riskController.ResumeTrading();
}

void AccountProtection::EmergencyStop(string reason = "")
{
    if (m_riskController != NULL)
        m_riskController.EmergencyStop(reason);
}

//+------------------------------------------------------------------+
//| Utility methods                                                  |
//+------------------------------------------------------------------+
double AccountProtection::GetTotalLotSize() const
{
    double totalLots = 0.0;
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
            totalLots += OrderLots();
    }
    return totalLots;
}

double AccountProtection::GetCurrentSpread(string symbol = "") const
{
    if (symbol == "") symbol = Symbol();
    return MarketInfo(symbol, MODE_SPREAD);
}

bool AccountProtection::IsNewTradingDay() const
{
    return (m_monitor != NULL) ? m_monitor.IsNewTradingDay() : false;
}

string AccountProtection::GetStatusDescription() const
{
    return (m_monitor != NULL) ? m_monitor.GetStatusDescription() : "Unknown";
}

//+------------------------------------------------------------------+
//| Get comprehensive protection summary                             |
//+------------------------------------------------------------------+
string AccountProtection::GetProtectionSummary() const
{
    string summary = "=== Account Protection Summary (Simplified) ===\n";

    if (m_config != NULL)
        summary += "Config: " + m_config.GetConfigSummary() + "\n\n";

    if (m_monitor != NULL)
        summary += "Monitor: " + m_monitor.GetMonitoringSummary() + "\n\n";

    if (m_riskController != NULL)
        summary += "Control: " + m_riskController.GetControlSummary();

    return summary;
}

#endif // ACCOUNT_PROTECTION_MQH
